# Breathly

A Flutter application designed to help users practice breathing exercises to reduce stress and improve mental wellbeing.

## Features

- **Main Screen** with a clean UI displaying different breathing exercises
- **Breathing Exercises**:
  - Calm (4-4-4 breathing)
  - Sleep (4-7-8 breathing)
  - Energy (rapid breathing)
- **Gamification** with points system
- **Mood Tracking** after each exercise
- **Settings** for personalization

## Getting Started

### Prerequisites

- Flutter SDK (^3.6.0)
- Dart SDK (^3.6.0)

### Installation

1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   flutter pub get
   ```
4. Run the app:
   ```bash
   flutter run
   ```

## Technologies Used

- Flutter
- Provider for state management
- SQLite for local database storage
- SharedPreferences for persistent settings

## License

This project is licensed under the MIT License.
